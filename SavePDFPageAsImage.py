import asyncio
import logging
import os
from os.path import exists
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import time
from collections import deque

# Асинхронные библиотеки для I/O
import aiofiles
import aiofiles.os
import asyncpg  # Замена для psycopg2

# CPU-bound библиотеки, которые будут выполняться в отдельных потоках
import fitz  # PyMuPDF
from PIL import Image

# Импорты AI-функций
from Gemini.GeminiAI import extract_entity_from_file_async
from Gemini.GoogleDocumentAI import google_ocr_documentai_async
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_async
from Mistral.MistralOCR import extract_ocr_data_by_mistral

from prompt import PROMPT_CLEAR_TEXT, PROMPT_OCR_CONTROL

# --- Константы и Настройки ---
MAX_IMAGE_SIZE_MB = 19
MIN_DPI = 300
MAX_DPI = 500
DPI_STEP = 25
MAX_PIXEL_DIMENSION = 4900
MAX_JPEG_SIZE = 9000

# Настройки для Rate Limiting
GEMINI_MAX_REQUESTS_PER_MINUTE = 8  # Немного меньше лимита для безопасности
GEMINI_REQUEST_INTERVAL = 60 / GEMINI_MAX_REQUESTS_PER_MINUTE  # секунд между запросами
MAX_CONCURRENT_TASKS = 5  # Максимальное количество одновременных задач
MAX_RETRIES = 3  # Максимальное количество повторных попыток
RETRY_BASE_DELAY = 2  # Базовая задержка для экспоненциального backoff

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Глобальный rate limiter для Gemini API
class RateLimiter:
    def __init__(self, max_requests_per_minute: int):
        self.max_requests = max_requests_per_minute
        self.request_times = deque()
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        async with self.lock:
            now = time.time()
            # Удаляем старые запросы (старше минуты)
            while self.request_times and now - self.request_times[0] > 60:
                self.request_times.popleft()
            
            # Если достигли лимита, ждем
            if len(self.request_times) >= self.max_requests:
                sleep_time = 60 - (now - self.request_times[0]) + 1
                if sleep_time > 0:
                    logger.info(f"Rate limit: ожидание {sleep_time:.1f} сек")
                    await asyncio.sleep(sleep_time)
                    return await self.acquire()
            
            # Добавляем текущий запрос
            self.request_times.append(now)
    
    async def release_on_error(self):
        """Освобождает место при ошибке"""
        async with self.lock:
            if self.request_times:
                self.request_times.pop()

# Создаем глобальный rate limiter
gemini_rate_limiter = RateLimiter(GEMINI_MAX_REQUESTS_PER_MINUTE)

# Функция для принудительной очистки rate limiter при зависании
async def force_reset_rate_limiter():
    """Принудительно сбрасывает rate limiter если система зависла"""
    async with gemini_rate_limiter.lock:
        old_count = len(gemini_rate_limiter.request_times)
        gemini_rate_limiter.request_times.clear()
        if old_count > 0:
            logger.warning(f"🔄 Принудительно очищен rate limiter: удалено {old_count} зависших запросов")

# --- Параметры подключения к БД (из переменных окружения) ---
DB_PARAMS = {
    'host': os.getenv("PG_HOST_LOCAL", "localhost"),
    'database': os.getenv("PG_DBNAME", "postgres"),
    'user': os.getenv("PG_USER", "postgres"),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", "5432"))
}

# Функция для безопасного выполнения запросов к Gemini API
async def safe_gemini_request(file_path: str, prompt: str, max_retries: int = MAX_RETRIES, timeout: int = 60) -> str:
    """
    Безопасно выполняет запрос к Gemini API с rate limiting, retry логикой и таймаутом.
    """
    logger.debug(f"🚀 Начинаем safe_gemini_request для {Path(file_path).name}")

    for attempt in range(max_retries):
        acquired = False
        try:
            logger.debug(f"🔄 Попытка {attempt + 1}/{max_retries} для {Path(file_path).name}")

            # Ждем разрешения от rate limiter
            logger.debug(f"⏳ Ожидаем разрешения от rate limiter для {Path(file_path).name}")
            await gemini_rate_limiter.acquire()
            acquired = True
            logger.debug(f"✅ Разрешение получено от rate limiter для {Path(file_path).name}")

            logger.info(f"📡 Выполняется запрос к Gemini API для {Path(file_path).name}, попытка {attempt + 1}")

            # Выполняем запрос с таймаутом
            logger.debug(f"🔍 Вызываем extract_entity_from_file_async для {Path(file_path).name}")
            result = await asyncio.wait_for(
                extract_entity_from_file_async(file_path, prompt),
                timeout=timeout
            )
            logger.debug(f"📥 Получен ответ от Gemini API для {Path(file_path).name}")

            if result and len(result.strip()) > 0:
                logger.info(f"✅ Успешный запрос к Gemini API для {Path(file_path).name}, длина ответа: {len(result)} символов")
                return result
            else:
                logger.warning(f"⚠️ Пустой ответ от Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
                # При пустом ответе освобождаем место для повтора
                await gemini_rate_limiter.release_on_error()
                acquired = False

        except asyncio.TimeoutError:
            logger.error(f"⏰ Таймаут ({timeout}с) при запросе к Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
            # При таймауте освобождаем место для повтора
            if acquired:
                await gemini_rate_limiter.release_on_error()
                acquired = False
            # Прерываем попытки при таймауте - не тратим время на повторы
            break

        except Exception as e:
            error_msg = str(e)

            # При любой ошибке освобождаем место
            if acquired:
                await gemini_rate_limiter.release_on_error()
                acquired = False

            # Проверяем, является ли это ошибкой rate limit
            if "429" in error_msg or "quota" in error_msg.lower():
                retry_delay = RETRY_BASE_DELAY * (2 ** attempt)  # Экспоненциальная задержка
                logger.warning(f"🚫 Rate limit для Gemini API. Повтор через {retry_delay} сек. Попытка {attempt + 1}/{max_retries}")
                await asyncio.sleep(retry_delay)
                continue
            else:
                logger.error(f"❌ Ошибка Gemini API для {Path(file_path).name}: {error_msg}")
                break

    logger.error(f"💥 Не удалось получить результат от Gemini API для {Path(file_path).name} после {max_retries} попыток")
    return ""


def get_file_extension(file_path: str) -> str:
    return Path(file_path).suffix.lower().lstrip('.')


def _save_pdf_page_as_image_sync_worker(input_path: str, page_number: int, dpi: int, jpeg_quality: int,
                                        max_size: int) -> str:
    """
    Синхронный воркер для CPU-bound операций. Выполняется в to_thread.
    """
    output_dir = "temp_image"
    os.makedirs(output_dir, exist_ok=True)

    doc = fitz.open(input_path)
    try:
        if not (1 <= page_number <= len(doc)):
            raise ValueError(f"Страница {page_number} отсутствует в документе (всего {len(doc)} стр.).")

        page = doc.load_page(page_number - 1)
        pix = page.get_pixmap(dpi=dpi)
        img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)

        if pix.width > max_size or pix.height > max_size:
            scale = min(max_size / pix.width, max_size / pix.height)
            new_size = (int(pix.width * scale), int(pix.height * scale))
            img = img.resize(new_size, Image.Resampling.LANCZOS)
            logger.info(f"Изображение масштабировано до {new_size} из-за лимита {max_size}px")

        base_name = f"{Path(input_path).stem}_page_{page_number}.jpg"
        img_path = os.path.join(output_dir, base_name)
        img.save(img_path, format="JPEG", quality=jpeg_quality)
        return img_path
    finally:
        doc.close()


async def save_pdf_page_as_image_async(input_path: str, page_number: int, dpi: int = 300, jpeg_quality: int = 90,
                                       max_size: int = MAX_JPEG_SIZE) -> Optional[str]:
    """
    Асинхронно извлекает страницу из PDF, выполняя тяжелые операции в отдельном потоке.
    """
    if not await aiofiles.os.path.exists(input_path):
        logger.error(f"Входной файл не существует: {input_path}")
        return None
    try:
        # Выносим всю синхронную, CPU-bound работу в отдельный поток
        img_path = await asyncio.to_thread(
            _save_pdf_page_as_image_sync_worker,
            input_path, page_number, dpi, jpeg_quality, max_size
        )
        logger.info(f"Сохранена страница {page_number} как изображение: {img_path}")
        return img_path
    except Exception as e:
        logger.error(f"Ошибка при сохранении страницы {page_number} из файла {input_path}: {e}", exc_info=True)
        return None


# ==============================================================================
# РАБОТА С БАЗОЙ ДАННЫХ (asyncpg)
# ==============================================================================

async def add_to_db_async(data: List[Dict[str, Any]]):
    """
    Асинхронно добавляет данные в базу данных, используя asyncpg.
    """
    if not data:
        return 0

    sql = """
        INSERT INTO t_scan_documents_raw (full_path, page_number, description, file_name, created_at)
        VALUES ($1, $2, $3, $4, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET description = EXCLUDED.description,
            full_path = EXCLUDED.full_path,
            created_at = now();
    """
    values = [
        (
            doc.get('full_path'),
            doc.get('page_number'),
            doc.get('description'),
            doc.get('file_name')
        ) for doc in data
    ]

    conn = None
    successful_count = 0
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        # executemany для массовой вставки при ON CONFLICT НЕ РАБОТАЕТ
        # Обрабатываем каждую запись отдельно для сохранения максимального количества данных
        for i, value in enumerate(values):
            try:
                await conn.execute(sql, *value)
                successful_count += 1
            except Exception as record_error:
                logger.warning(f"Ошибка при сохранении записи {i+1}/{len(values)}: {record_error}")
                continue

        logger.info(f"Данные OCR: успешно сохранено {successful_count}.")
        return successful_count
    except Exception as e:
        logger.error(f"Критическая ошибка при подключении к БД: {e}", exc_info=True)
        return successful_count
    finally:
        if conn:
            await conn.close()


async def process_image_async(pdf_or_image_path: str, page_number: int, repeat:int = 3) -> Dict[str, Any]:
    """
    Асинхронно обрабатывает одну страницу/изображение: сохраняет, делает OCR, очищает и пишет в БД.
    """
    data = {
        'full_path': pdf_or_image_path,
        'page_number': page_number,
        'description': None,
        'file_name': Path(pdf_or_image_path).name
    }
    temp_file_name = None

    try:
        # Проверяем существование файла перед обработкой
        if not await aiofiles.os.path.exists(pdf_or_image_path):
            logger.warning(f"Файл для повторной обработки не найден: {pdf_or_image_path}")
            return {}
        
        temp_file_name = None
        ext = get_file_extension(pdf_or_image_path)
        if ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
            temp_file_name = pdf_or_image_path
        elif ext == 'pdf':
            temp_file_name = await save_pdf_page_as_image_async(pdf_or_image_path, page_number)
        else:
            logger.warning(f"Неподдерживаемый формат файла для OCR: {ext}")
            return {}

        if not exists(temp_file_name):
            logger.error("Не удалось создать временный файл изображения.")
            return {}

        # --- Безопасное выполнение OCR запроса ---
        # Используем только один запрос с rate limiting
        logger.info(f"🔍 Начинаем OCR обработку для {Path(pdf_or_image_path).name} стр.{page_number}")
        ocr_text = await safe_gemini_request(temp_file_name, PROMPT_OCR_CONTROL)
        logger.info(f"{ocr_text[:200]}...")
        logger.info(f"📄 OCR завершен для {Path(pdf_or_image_path).name} стр.{page_number}, длина: {len(ocr_text)} символов")

        # Очистка текста и запись в БД
        if ocr_text and len(ocr_text.strip()) > 100 and len(ocr_text.strip()) < 10000:  # Минимальный порог для осмысленного текста
            try:
                # logger.info(f"🔧 Начинаем обработку текста Mistral API для {Path(pdf_or_image_path).name} стр.{page_number}")
                # correct_ocr_text = await extract_data_from_text_by_mistral_async(ocr_text, PROMPT_CLEAR_TEXT)
                # logger.info(f"✅ Mistral API завершен для {Path(pdf_or_image_path).name} стр.{page_number}")
                correct_ocr_text = ocr_text
                data['description'] = correct_ocr_text if correct_ocr_text else None  # None потом еще раз пройдемся, чем заносить некорректный текст
            except Exception as e:
                logger.error(f"❌ Ошибка при обработке текста Mistral API: {e}")
                # data['description'] = ocr_text.strip()  # None потом еще раз пройдемся, чем заносить некорректный текст
                data['description'] = None
        else:
            logger.info(f"⏭️ Пропускаем Mistral обработку для {Path(pdf_or_image_path).name} стр.{page_number} (длина текста: {len(ocr_text.strip()) if ocr_text else 0})")
            # data['description'] = ocr_text.strip() if ocr_text else None
            if repeat > 0:
                logger.info(f"🔄 Повторная обработка для {Path(pdf_or_image_path).name} стр.{page_number}")
                await process_image_async(pdf_or_image_path, page_number, repeat-1)
            data['description'] = None  # None потом еще раз пройдемся, чем заносить некорректный текст

        logger.info(f"💾 Сохраняем в БД данные для {Path(pdf_or_image_path).name} стр.{page_number}")
        await add_to_db_async([data])
        logger.info(f"✅ Завершена обработка {Path(pdf_or_image_path).name} стр.{page_number}")
        return data

    except Exception as e:
        logger.error(f"Ошибка при обработке {pdf_or_image_path} стр.{page_number}: {e}", exc_info=True)
        return {}

    finally:
        # Удаляем временный файл, если он был создан из PDF
        if temp_file_name and get_file_extension(pdf_or_image_path) == 'pdf' and await aiofiles.os.path.exists(temp_file_name):
            try:
                await aiofiles.os.remove(temp_file_name)
            except Exception as e:
                logger.warning(f"Не удалось удалить временный файл {temp_file_name}: {e}")


async def extract_pages_from_pdf_async(pdf_or_image_path: str, page_numbers: Optional[List[int]] = None):
    """
    Асинхронно извлекает данные со страниц PDF или изображения.
    Обрабатывает страницы с ограничением параллельности.
    """
    if not await aiofiles.os.path.exists(pdf_or_image_path):
        logger.error(f"Файл не найден: {pdf_or_image_path}")
        return

    if not page_numbers:
        # Если номера страниц не указаны, получаем их количество
        try:
            # Это быстрая синхронная операция, можно выполнить напрямую или в to_thread для 100% неблокировки
            doc = await asyncio.to_thread(fitz.open, pdf_or_image_path)
            total_pages = len(doc)
            doc.close()
            page_numbers = list(range(1, total_pages + 1))
        except Exception as e:
            logger.error(f"Не удалось прочитать PDF файл {pdf_or_image_path}: {e}")
            return

    # Обрабатываем страницы последовательно для избежания блокировок
    successful_results = []

    for i, page_num in enumerate(page_numbers):
        try:
            logger.info(f"🔄 Обрабатываем страницу {i+1}/{len(page_numbers)}: {page_num}")

            # Обрабатываем с таймаутом
            result = await asyncio.wait_for(
                process_image_async(pdf_or_image_path, page_num),
                timeout=180  # 3 минуты на одну страницу
            )

            if result:
                successful_results.append(result)
                logger.info(f"✅ Страница {page_num} обработана успешно")
            else:
                logger.warning(f"⚠️ Страница {page_num} - пустой результат")

        except asyncio.TimeoutError:
            logger.error(f"⏰ Страница {page_num} - таймаут (3 мин)")
            # Принудительно очищаем rate limiter при таймауте
            await force_reset_rate_limiter()

        except Exception as e:
            logger.error(f"❌ Страница {page_num} - ошибка: {e}")

    return {'doc': successful_results}


async def extract_pdf_files_from_folder_async(folder_path: str):
    """
    Асинхронно находит все PDF в папке и обрабатывает их с ограничением параллельности.
    """
    pdf_files = []
    # os.walk - синхронный генератор, его можно оставить, т.к. он быстрый
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))

    logger.info(f"Найдено {len(pdf_files)} PDF файлов для обработки")

    # Обрабатываем файлы последовательно
    successful = 0
    failed = 0

    for i, pdf_file in enumerate(pdf_files):
        try:
            logger.info(f"🔄 Обрабатываем файл {i+1}/{len(pdf_files)}: {Path(pdf_file).name}")

            # Обрабатываем с таймаутом
            result = await asyncio.wait_for(
                extract_pages_from_pdf_async(pdf_file),
                timeout=1800  # 30 минут на один файл
            )

            if result:
                successful += 1
                logger.info(f"✅ Файл {Path(pdf_file).name} обработан успешно")
            else:
                failed += 1
                logger.warning(f"⚠️ Файл {Path(pdf_file).name} - пустой результат")

        except asyncio.TimeoutError:
            failed += 1
            logger.error(f"⏰ Файл {Path(pdf_file).name} - таймаут (30 мин)")
            await force_reset_rate_limiter()

        except Exception as e:
            failed += 1
            logger.error(f"❌ Файл {Path(pdf_file).name} - ошибка: {e}")

    logger.info(f"Обработка завершена. Успешно: {successful}, с ошибками: {failed}")


async def monitor_with_timeout(duration_minutes: int = 30):
    """Мониторинг с ограничением по времени"""
    end_time = time.time() + (duration_minutes * 60)
    
    while time.time() < end_time:
        await asyncio.sleep(180)
        logger.warning("🔄 Принудительная проверка системы...")
        await force_reset_rate_limiter()
        logger.info("🔄 Rate limiter очищен.")
    
    logger.info(f"Мониторинг завершен после {duration_minutes} минут")


async def reprocess_empty_descriptions():
    """
    Повторно обрабатывает записи с пустым description из базы данных.
    """
    conn = None
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        
        # Получаем записи с пустым description
        query = """
            SELECT full_path, page_number, file_name 
            FROM t_scan_documents_raw 
            WHERE COALESCE(description,'') = ''
            ORDER BY file_name, page_number;
        """
        
        rows = await conn.fetch(query)
        logger.info(f"Найдено {len(rows)} записей для повторной обработки")
        
        if not rows:
            logger.info("Нет записей для повторной обработки")
            return

        successful = 0
        failed = 0

        for i, row in enumerate(rows):
            try:
                logger.info(f"🔄 Обрабатываем запись {i+1}/{len(rows)}")
                file_path = os.path.join(r"c:\Scan\All\AlreadyAddToDb", row['file_name'])

                if not os.path.exists(file_path):
                    logger.error(f"Файл не найден: {file_path}")
                    continue

                # Обрабатываем с таймаутом
                result = await asyncio.wait_for(
                    process_image_async(file_path, row['page_number']),
                    timeout=180  # 3 минуты на одну запись
                )

                if result:
                    successful += 1
                    logger.info(f"✅ Запись {i+1}/{len(rows)} обработана успешно")
                else:
                    failed += 1
                    logger.warning(f"⚠️ Запись {i+1}/{len(rows)} - пустой результат")

            except asyncio.TimeoutError:
                failed += 1
                logger.error(f"⏰ Запись {i+1}/{len(rows)} - таймаут (3 мин)")
                # Принудительно очищаем rate limiter при таймауте
                await force_reset_rate_limiter()

            except Exception as e:
                failed += 1
                logger.error(f"❌ Запись {i+1}/{len(rows)} - ошибка: {e}")

        # Выводим финальную статистику
        logger.info(f"Повторная обработка завершена. Успешно: {successful}, с ошибками: {failed}")

    except Exception as e:
        logger.error(f"Ошибка при получении данных из БД для переобработки: {e}", exc_info=True)
    finally:
        if conn:
            await conn.close()


# ==============================================================================
# ТОЧКА ВХОДА
# ==============================================================================

async def main():
    """Главная асинхронная функция."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    logger.info("=== Запуск системы обработки документов ===")
    logger.info(f"Настройки Rate Limiting: {GEMINI_MAX_REQUESTS_PER_MINUTE} запросов/мин")
    logger.info(f"Максимальное количество параллельных задач: {MAX_CONCURRENT_TASKS}")

    start_time = time.time()

    try:
        # --- Выберите один из вариантов для запуска ---
        pdf_file_path = r"c:\Scan\All\AlreadyAddToDb\2024.09 Vozvrati 3.pdf" 

        # Вариант 1. Обработка всех pdf в папке с учетом вложенных.
        # logger.info("Запуск варианта 1: Обработка всех PDF в папке...")
        folder_path = r"c:\Scan\All\AlreadyAddToDb\r"
        await extract_pdf_files_from_folder_async(folder_path)

        # Вариант 2. Обработка одного pdf с указанием списка страниц.
        # logger.info("Запуск варианта 2: Обработка одного PDF с указанием страниц...")
        # page_numbers = [9,51,53,54,65]
        # result = await extract_pages_from_pdf_async(pdf_file_path, page_numbers)
        # print(result)

        # Вариант 3. Обработка одного pdf БЕЗ указания списка страниц.
        # logger.info("Запуск варианта 3: Обработка всех страниц одного PDF...")
        # result = await extract_pages_from_pdf_async(pdf_file_path)
        # print(result)

        # Вариант 4. Повторная обработка записей из БД.
        # logger.info("Запуск варианта 4: Повторная обработка данных из БД...")
        
        # # Запускаем мониторинг в фоне
        # monitor_task = asyncio.create_task(monitor_with_timeout(30))
        
        # # Запускаем обработку с таймаутом для предотвращения зависания
        # try:
        #     await asyncio.wait_for(reprocess_empty_descriptions(), timeout=1800)  # 30 минут максимум
        # except asyncio.TimeoutError:
        #     logger.error("⏰ Превышен общий таймаут обработки (30 мин). Принудительное завершение...")
        #     await force_reset_rate_limiter()
        # finally:
        #     # Отменяем мониторинг если обработка завершилась
        #     monitor_task.cancel()
        #     try:
        #         await monitor_task
        #     except asyncio.CancelledError:
        #         pass

        logger.info("Все варианты закомментированы. Выберите нужный вариант и раскомментируйте его.")

    except KeyboardInterrupt:
        logger.info("Получен сигнал прерывания. Завершение работы...")
    except Exception as e:
        logger.error(f"Критическая ошибка в main: {e}", exc_info=True)
    finally:
        elapsed_time = time.time() - start_time
        logger.info(f"Все операции завершены. Время выполнения: {elapsed_time:.2f} секунд")


if __name__ == "__main__":
    # Запускаем асинхронную программу
    asyncio.run(main())

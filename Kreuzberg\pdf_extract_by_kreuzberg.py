import asyncio
import os
import traceback

# Импортируем классы, которые, как мы точно знаем, существуют
from kreuzberg import extract_file, ExtractionConfig, TesseractConfig


async def extract_text_from_pdf_by_kreuzberg(pdf_path: str):
    """
    Асинхронно извлекает текст из PDF с корректным указанием языка для OCR.
    """
    if not os.path.exists(pdf_path):
        print(f"ОШИБКА: Файл не найден по пути: {pdf_path}")
        return None

    try:
        #
        # --- ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ ---
        # Создаем конфигурацию для Tesseract, используя правильный
        # параметр 'language', как указано в исходном коде библиотеки.
        #
        ocr_settings = TesseractConfig(language="ukr")

        # Создаем основную конфигурацию экстракции
        config = ExtractionConfig(ocr_config=ocr_settings, force_ocr=True)

        # print("Конфигурация создана верно. Начинаю обработку файла...")
        # print(f"Файл: {os.path.basename(pdf_path)}")
        # print(f"Язык OCR: '{ocr_settings.language}'")

        # Вызов функции извлечения текста
        result = await extract_file(pdf_path, config=config)

        # Обработка результата
        if result and result.content:
            return result.content
        else:
            print("\nОШИБКА: Не удалось извлечь текст. Документ может быть пустым или поврежденным.")
            return None

    except Exception as e:
        print(f"\n--- КРИТИЧЕСКАЯ ОШИБКА ВО ВРЕМЯ ВЫПОЛНЕНИЯ ---")
        print(f"Тип ошибки: {type(e).__name__}")
        print(f"Сообщение: {e}")
        print("\n--- Полная трассировка ошибки ---")
        traceback.print_exc()
        print("---------------------------------\n")
        return None


# Основной блок для запуска скрипта
if __name__ == "__main__":
    pdf_file_path = r"c:\Rasim\Python\Edin\32490244_ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ ЕПІЦЕНТР К\202409\Видаткова накладна\Видаткова накладна №10658 від 14 09 2024.pdf" 
    
    # Запускаем главную асинхронную функцию
    result = asyncio.run(extract_text_from_pdf_by_kreuzberg(pdf_file_path))
    print("\n--- ТЕКСТ УСПЕШНО ИЗВЛЕЧЕН ---")
    print(result)
    print("------------------------------\n")

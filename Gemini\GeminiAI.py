import ast
import base64
import json
import os
import sys
from os.path import exists
from typing import Dict, Any, Union

import fitz  # PyMuPDF
from dotenv import load_dotenv

# --- Асинхронные импорты ---
import asyncio
import aiofiles
import aiofiles.os

# --- Импорты для Google Gemini API ---
# Используем единый, современный способ импорта из второго скрипта
from google.generativeai import configure, GenerativeModel
from google.generativeai.types import GenerationConfig, ContentDict, PartDict
from .prompt import PROMPT_EXAMPLE_GEMINI, PROMPT_OCR_CONTROL

load_dotenv()

MODEL = "gemini-2.5-pro"  # os.getenv('GEMINI_MODEL_2F')  # "gemini-2.5-flash"  # "gemini-2.5-pro"
# Глобальная конфигурация API ключа
api_key = os.environ.get("GEMINI_API_KEY")  # GEMINI_API_KEY
if not api_key:
    raise ValueError("API ключ 'GEMINI_API_KEY' не найден в .env файле.")
configure(api_key=api_key)


# ==============================================================================
# СИНХРОННЫЕ ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ (из первого скрипта)
# ==============================================================================

def encode_pdf(pdf_path: str) -> Union[str, None]:
    """Синхронно кодирует файл в base64."""
    try:
        if not os.path.exists(pdf_path):
            return None
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except Exception as e:
        print(f"Ошибка при кодировании файла: {e}")
        return None

def get_pages_count(file_path: str) -> int:
    """Синхронно получает количество страниц в PDF."""
    if file_path.lower().endswith('.pdf'):
        try:
            doc = fitz.open(file_path)
            pages_count = len(doc)
            doc.close()
            return pages_count
        except Exception as e:
            print(f"Ошибка при чтении PDF: {e}")
            return 1
    return 1

def get_file_extension(file_path: str) -> Union[str, None]:
    if not exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None
    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')

def get_mime_type(file_path: str) -> str:
    """Синхронно определяет MIME-тип файла."""
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'bmp', 'tiff']:
        return f'image/{ext}'
    elif ext in ['jpg', 'jpeg']:
        return 'image/jpeg'
    return 'text/plain'

def smart_parse(obj: str) -> Any:
    """Пытается распарсить строку как JSON или Python литерал."""
    if isinstance(obj, (dict, list)):
        return obj
    try:
        return json.loads(obj)
    except json.JSONDecodeError:
        try:
            return ast.literal_eval(obj)
        except (ValueError, SyntaxError):
            return obj # Возвращаем как есть, если не удалось распарсить

def clear_text(json_string: str) -> Any:
    """Очищает ответ модели от markdown и парсит его."""
    if isinstance(json_string, str):
        try:
            text = json_string.strip()
            if text.startswith('```json'):
                text = text[7:]
            if text.endswith('```'):
                text = text[:-3]
            return smart_parse(text)
        except Exception as e:
            print(f"Ошибка при очистке текста: {e}")
            return json_string
    return json_string

# ==============================================================================
# СИНХРОННЫЙ ВЫЗОВ API (из первого скрипта, адаптирован под новую библиотеку)
# ==============================================================================

def extract_entity_from_page_by_gemini(pdf_path: str, prompt: str = PROMPT_EXAMPLE_GEMINI) -> Union[str, None]:
    """Синхронно извлекает данные из файла с помощью Gemini."""
    if not exists(pdf_path):
        print(f"Файл не найден: {pdf_path}")
        return None

    # Используем стандартное чтение файла, так как функция синхронная
    with open(pdf_path, "rb") as f:
        data = f.read()

    mime_type = get_mime_type(pdf_path)

    # Инициализация модели (модель та же, что и в вашем коде)
    model = GenerativeModel(model_name=MODEL)  # "gemini-2.5-flash"  # "gemini-2.5-pro"

    contents = [
        {"role": "user", "parts": [
            {"mime_type": mime_type, "data": data},
            {"text": prompt}
        ]}
    ]

    try:
        print("Выполняется синхронный запрос к Gemini API...")
        response = model.generate_content(contents)
        return response.text
    except Exception as e:
        print(f"Ошибка при синхронном обращении к Gemini API: {e}")
        return None


# ==============================================================================
# АСИНХРОННЫЕ ФУНКЦИИ (из второго скрипта)
# ==============================================================================

async def encode_file_async(file_path: str) -> Union[bytes, None]:
    """Асинхронно читает файл и возвращает его содержимое в виде байтов."""
    if not await aiofiles.os.path.exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None
    async with aiofiles.open(file_path, "rb") as f:
        return await f.read()

async def get_mime_type_async(file_path: str) -> str:
    """Асинхронно определяет MIME-тип файла."""
    ext = os.path.splitext(file_path)[-1].lower().lstrip(".")
    if ext == "pdf":
        return "application/pdf"
    elif ext in ["jpg", "jpeg"]:
        return "image/jpeg"
    elif ext in ["png", "bmp", "tiff"]:
        return f"image/{ext}"
    return "application/octet-stream"

async def extract_entity_from_file_async(file_path: str, prompt: str = PROMPT_OCR_CONTROL) -> Union[str, None]:
    """Асинхронно извлекает данные из файла с помощью Gemini."""
    data = await encode_file_async(file_path)
    if not data:
        return None

    mime_type = await get_mime_type_async(file_path)

    # Инициализация модели (модель та же, что и в вашем коде)
    model = GenerativeModel(
        model_name=MODEL, # "gemini-2.5-flash",  # "gemini-2.5-pro"
        generation_config=GenerationConfig(temperature=0.2)
    )

    contents = [
        {"role": "user", "parts": [
            {"mime_type": mime_type, "data": data},
            {"text": prompt}
        ]}
    ]

    try:
        print("Выполняется АСИНХРОННЫЙ запрос к Gemini API...")
        response = await model.generate_content_async(contents)
        return response.text
    except Exception as e:
        print(f"Ошибка при асинхронном обращении к Gemini API: {e}")
        return None

# ==============================================================================
# ГЛАВНЫЙ БЛОК ВЫПОЛНЕНИЯ
# ==============================================================================

async def main_async():
    """Асинхронная главная функция для демонстрации."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    pdf_path = r"C:\Rasim\Python\ScanDocument\temp_image\202410 Merge_page_99.png"
    if not exists(pdf_path):
        print(f"Тестовый файл не найден: {pdf_path}")
        return

    # --- Пример вызова СИНХРОННОЙ функции ---
    print("--- 1. Тестирование СИНХРОННОЙ функции ---")
    # В асинхронной функции main мы можем вызвать синхронный код через to_thread
    sync_result = await asyncio.to_thread(extract_entity_from_page_by_gemini, pdf_path, PROMPT_OCR_CONTROL)
    print("\n[СИНХРОННЫЙ РЕЗУЛЬТАТ]:")
    print(sync_result)
    print("-" * 40)

    # --- Пример вызова АСИНХРОННОЙ функции ---
    print("\n--- 2. Тестирование АСИНХРОННОЙ функции ---")
    async_result = await extract_entity_from_file_async(pdf_path, PROMPT_OCR_CONTROL)
    print("\n[АСИНХРОННЫЙ РЕЗУЛЬТАТ]:")
    print(async_result)
    print("-" * 40)


if __name__ == "__main__":
    # Запускаем асинхронную главную функцию
    asyncio.run(main_async())